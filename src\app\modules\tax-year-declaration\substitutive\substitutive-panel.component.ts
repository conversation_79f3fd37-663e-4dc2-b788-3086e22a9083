import { Component, Input, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { Form, FormBuilder, FormGroup } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { Subject } from 'rxjs';
import { Substitutiva } from '../tax-year-declaration.model';
import { TaxYearDeclarationService } from '../services/tax-declaration.service';
import { Column, Row } from 'se-ui-components-mf-lib';

@Component({
  selector: 'app-substitutive-panel',
  templateUrl: './substitutive-panel.component.html',
  styleUrls: [],
})
export class SubstitutivePanelComponent implements OnInit, OnDestroy {

  @Input() set taxYear(value: string | null) {
    console.log('value', value);
    
    if (value) {
      this.alertInfo = this.translateService.instant('SE_DECINF_MF.MODULE_TAX_YEAR_DECLARATION.SUBSTITUTIVE_PANEL.ALERT_INFO', {taxYear: value} )
    }
  }
  @Input() set substitutiva(value: Substitutiva) {
    console.log('substitu', value);
    this.tableData = 
  }

  // OBSERVABLES CONTROL DESTROY
  private destroyed$: Subject<void> = new Subject();
  protected alertInfo: string = this.translateService.instant('SE_DECINF_MF.MODULE_TAX_YEAR_DECLARATION.SUBSTITUTIVE_PANEL.ALERT_INFO');
  protected tableColumns: Column[] = this.taxDeclarationService.getSubstitutiveTableColumns();
  protected tableData: Row[] = [];
  protected componentForm: FormGroup;

  constructor(private translateService: TranslateService, private fb: FormBuilder, private taxDeclarationService: TaxYearDeclarationService) {
    this.componentForm = this.fb.group({
        substitutiva : [false],
    });
  }

  ngOnInit(): void {
    // Initialization logic here
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
  }
}
